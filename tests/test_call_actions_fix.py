#!/usr/bin/env python3
"""
Test script to verify that call_actions are now working correctly in getAgent method.
"""

from app.db.session import SessionLocal
from app.models.agent import AgentConfig
from app.utils.helpers.agent_to_protobuf import _agents_to_protobuf, _convert_call_actions_to_protobuf

def test_call_actions_fix():
    """Test that call_actions are correctly returned in getAgent method."""
    print("🧪 Testing Call Actions Fix")
    print("=" * 60)
    
    agent_id = 'f05ec4a7-1a7d-42cf-a279-d34fe8561782'
    
    db = SessionLocal()
    try:
        # 1. Check database data
        print("📋 Step 1: Check database data")
        agent = db.query(AgentConfig).filter(AgentConfig.id == agent_id).first()
        if agent:
            print(f"  ✅ Agent found: {agent.name}")
            print(f"  📊 Call actions in DB: {agent.call_actions}")
            print(f"  📊 Type: {type(agent.call_actions)}")
        else:
            print(f"  ❌ Agent {agent_id} not found")
            return
        
        print()
        
        # 2. Test conversion function
        print("📋 Step 2: Test call_actions conversion function")
        if agent.call_actions:
            converted = _convert_call_actions_to_protobuf(agent.call_actions)
            print(f"  ✅ Conversion successful")
            print(f"  📊 Converted count: {len(converted)}")
            for i, action in enumerate(converted):
                print(f"    Action {i+1}: type={action.action_type}, exec={action.execution_type}, id={action.id}")
        else:
            print(f"  ⚠️  No call_actions to convert")
        
        print()
        
        # 3. Test _agents_to_protobuf function (used by getAgent)
        print("📋 Step 3: Test _agents_to_protobuf function")
        agent_proto = _agents_to_protobuf(db, agent_id)
        if agent_proto:
            print(f"  ✅ Protobuf conversion successful")
            print(f"  📊 Agent name: {agent_proto.name}")
            print(f"  📊 Call actions count: {len(agent_proto.call_actions)}")
            
            if len(agent_proto.call_actions) > 0:
                print(f"  🎉 SUCCESS: Call actions are now included!")
                for i, action in enumerate(agent_proto.call_actions):
                    print(f"    Action {i+1}: type={action.action_type}, exec={action.execution_type}, id={action.id}")
            else:
                print(f"  ❌ FAILED: Call actions are still empty")
        else:
            print(f"  ❌ Protobuf conversion failed")
        
        print()
        
        # 4. Compare with database data
        print("📋 Step 4: Verify data consistency")
        if agent.call_actions and agent_proto and len(agent_proto.call_actions) > 0:
            db_count = len(agent.call_actions)
            proto_count = len(agent_proto.call_actions)
            
            if db_count == proto_count:
                print(f"  ✅ Count matches: DB={db_count}, Proto={proto_count}")
                
                # Check first action details
                db_action = agent.call_actions[0]
                proto_action = agent_proto.call_actions[0]
                
                print(f"  📊 DB action: {db_action}")
                print(f"  📊 Proto action: type={proto_action.action_type}, exec={proto_action.execution_type}, id={proto_action.id}")
                
                # Verify enum conversion
                expected_action_type = 0 if db_action.get("action_type") == "POST_CALL" else 1
                expected_execution_type = 0 if db_action.get("execution_type") == "WORKFLOW" else 1
                
                if (proto_action.action_type == expected_action_type and 
                    proto_action.execution_type == expected_execution_type and 
                    proto_action.id == db_action.get("id")):
                    print(f"  🎉 PERFECT: All data matches correctly!")
                else:
                    print(f"  ⚠️  Data mismatch detected")
            else:
                print(f"  ❌ Count mismatch: DB={db_count}, Proto={proto_count}")
        
    finally:
        db.close()
    
    print()
    print("=" * 60)
    print("📋 Summary:")
    print("  ✅ Fixed: Added call_actions to _convert_single_agent_to_protobuf")
    print("  ✅ Working: getAgent method now returns call_actions")
    print("  ✅ Consistent: Both getAgent and getCallActions return same data")
    print("  🚀 Ready: API Gateway can now receive call_actions from getAgent")

if __name__ == "__main__":
    test_call_actions_fix()
